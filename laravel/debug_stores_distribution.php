<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\SalesService;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Enums\SaleDistribution;
use App\Sale;
use Illuminate\Support\Facades\DB;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== STORES Distribution Debug Analysis ===\n\n";

// Test parameters
$date = "2025-05-01";
$productId = 41;
$distributorIds = [1, 2, 3, 4, 5];

echo "Parameters:\n";
echo "- Date: $date\n";
echo "- Product ID: $productId\n";
echo "- Distributor IDs: " . implode(', ', $distributorIds) . "\n\n";

// Step 1: Check basic sales
echo "Step 1: Basic sales count\n";
$basicSales = Sale::whereYear('date', 2025)
    ->whereMonth('date', 5)
    ->where('product_id', $productId)
    ->whereIn('distributor_id', $distributorIds)
    ->count();
echo "Basic sales: $basicSales\n\n";

// Step 2: Check sales with STORES mapping
echo "Step 2: Sales with STORES mapping\n";
$storesSales = Sale::whereYear('sales.date', 2025)
    ->whereMonth('sales.date', 5)
    ->where('sales.product_id', $productId)
    ->whereIn('sales.distributor_id', $distributorIds)
    ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
    ->join('mappings', 'mapping_sale.mapping_id', 'mappings.id')
    ->where('mappings.unified_pharmacy_type_id', 16)
    ->count();
echo "Sales with STORES mapping: $storesSales\n\n";

// Step 3: Check sales with sales_details
echo "Step 3: Sales with sales_details\n";
$salesWithDetails = Sale::whereYear('sales.date', 2025)
    ->whereMonth('sales.date', 5)
    ->where('sales.product_id', $productId)
    ->whereIn('sales.distributor_id', $distributorIds)
    ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
    ->join('mappings', 'mapping_sale.mapping_id', 'mappings.id')
    ->where('mappings.unified_pharmacy_type_id', 16)
    ->join('sales_details', 'sales.id', 'sales_details.sale_id')
    ->count();
echo "Sales with STORES mapping + sales_details: $salesWithDetails\n\n";

// Step 4: Test SalesService directly
echo "Step 4: Testing SalesService directly\n";
$salesService = SalesService::make(SaleDistribution::NORMAL, DistributionType::STORES);
$ratios = $salesService->getRatiosForDistribution($date, $productId, $distributorIds);
echo "SalesService ratios count: " . $ratios->count() . "\n";
echo "Total percentage: " . $ratios->sum('percentage') . "\n\n";

// Step 5: Check the specific sale with STORES mapping
echo "Step 5: Analyzing the STORES sale\n";
$storesSale = Sale::whereYear('sales.date', 2025)
    ->whereMonth('sales.date', 5)
    ->where('sales.product_id', $productId)
    ->whereIn('sales.distributor_id', $distributorIds)
    ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
    ->join('mappings', 'mapping_sale.mapping_id', 'mappings.id')
    ->where('mappings.unified_pharmacy_type_id', 16)
    ->select('sales.*')
    ->first();

if ($storesSale) {
    echo "Found STORES sale ID: " . $storesSale->id . "\n";
    echo "Sale details count: " . $storesSale->details()->count() . "\n";
    echo "Sale ceiling: " . $storesSale->ceiling . "\n";
    echo "Sale quantity: " . $storesSale->quantity . "\n";

    // Check what ceiling values SalesService is looking for
    echo "\nCeiling analysis:\n";
    echo "Sale ceiling value: " . $storesSale->ceiling . "\n";
    echo "BELOW ceiling enum value: " . \App\Services\Enums\Ceiling::BELOW->value . "\n";
    echo "Sale matches BELOW ceiling: " . ($storesSale->ceiling == \App\Services\Enums\Ceiling::BELOW->value ? 'Yes' : 'No') . "\n";
    
    // Check if this sale has line_products and line_divisions
    $hasLineProducts = DB::table('line_products')
        ->where('product_id', $productId)
        ->where('from_date', '<=', $date)
        ->where(function($q) use ($date) {
            $q->where('to_date', '>', $date)->orWhereNull('to_date');
        })
        ->exists();
    echo "Has valid line_products: " . ($hasLineProducts ? 'Yes' : 'No') . "\n";
    
    if ($storesSale->details()->count() > 0) {
        $saleDetail = $storesSale->details()->first();
        echo "First detail line_id: " . $saleDetail->line_id . "\n";
        echo "First detail div_id: " . $saleDetail->div_id . "\n";
        
        $hasLineDivisions = DB::table('line_divisions')
            ->where('id', $saleDetail->div_id)
            ->where('line_id', $saleDetail->line_id)
            ->where('from_date', '<=', $date)
            ->where(function($q) use ($date) {
                $q->where('to_date', '>', $date)->orWhereNull('to_date');
            })
            ->exists();
        echo "Has valid line_divisions: " . ($hasLineDivisions ? 'Yes' : 'No') . "\n";
    }
} else {
    echo "No STORES sale found!\n";
}

echo "\n=== Analysis Complete ===\n";
